<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UangJalan extends Model
{
    use SoftDeletes;

    protected $table = 'uang_jalan';

    protected $fillable = [
        'id_do',
        'nominal',
        'uang_depot',
        'uang_jalan_amount',
        'uang_bongkar',
        'uang_pas',
        'uang_lembur',
        'uang_bbm',
        'uang_tol',
        'keterangan_depot',
        'keterangan_jalan',
        'keterangan_bongkar',
        'keterangan_pas',
        'keterangan_lembur',
        'keterangan_bbm',
        'keterangan_tol',
        'status_kirim',
        'bukti_kirim',
        'status_terima',
        'bukti_terima',
        'approval_status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'id_user',
        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'approved_at' => 'datetime',
        'nominal' => 'decimal:2',
        'uang_depot' => 'decimal:2',
        'uang_jalan_amount' => 'decimal:2',
        'uang_bongkar' => 'decimal:2',
        'uang_pas' => 'decimal:2',
        'uang_lembur' => 'decimal:2',
        'uang_bbm' => 'decimal:2',
        'uang_tol' => 'decimal:2',
    ];

    public function deliveryOrder()
    {
        return $this->belongsTo(DeliveryOrder::class, 'id_do');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'id_user');
    }

    // Alias for driver (same as user)
    public function driver()
    {
        return $this->user();
    }

    /**
     * Calculate total allowance from all breakdown components
     */
    public function calculateTotal(): float
    {
        return (float) (
            $this->uang_depot +
            $this->uang_jalan_amount +
            $this->uang_bongkar +
            $this->uang_pas +
            $this->uang_lembur +
            $this->uang_bbm +
            $this->uang_tol
        );
    }

    /**
     * Get breakdown as array
     */
    public function getBreakdownArray(): array
    {
        return [
            'uang_depot' => [
                'amount' => $this->uang_depot,
                'label' => 'Uang Depot',
                'keterangan' => $this->keterangan_depot,
            ],
            'uang_jalan' => [
                'amount' => $this->uang_jalan_amount,
                'label' => 'Uang Jalan',
                'keterangan' => $this->keterangan_jalan,
            ],
            'uang_bongkar' => [
                'amount' => $this->uang_bongkar,
                'label' => 'Uang Bongkar',
                'keterangan' => $this->keterangan_bongkar,
            ],
            'uang_pas' => [
                'amount' => $this->uang_pas,
                'label' => 'Uang Pas',
                'keterangan' => $this->keterangan_pas,
            ],
            'uang_lembur' => [
                'amount' => $this->uang_lembur,
                'label' => 'Uang Lembur',
                'keterangan' => $this->keterangan_lembur,
            ],
            'uang_bbm' => [
                'amount' => $this->uang_bbm,
                'label' => 'Uang BBM',
                'keterangan' => $this->keterangan_bbm,
            ],
            'uang_tol' => [
                'amount' => $this->uang_tol,
                'label' => 'Uang Tol',
                'keterangan' => $this->keterangan_tol,
            ],
        ];
    }

    /**
     * Auto-update nominal when breakdown changes
     */
    protected static function booted()
    {
        static::saving(function ($uangJalan) {
            $uangJalan->nominal = $uangJalan->calculateTotal();
        });
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Approval workflow methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function approve(User $approver, ?string $notes = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        return $this->update([
            'approval_status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    public function reject(User $approver, ?string $notes = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        return $this->update([
            'approval_status' => 'rejected',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }
}
